## 本说明是Air780E  GSM模块的AT指令说明文档，请根据文档编制代码
## 下面的AT指令测试是通过串口助手连接模块实测数据
## AT指令收到的信息都打印输出，输出信息使用英文，中文会乱码的
## AT指令发送后 while等待接收回复，如果超时 打印输出AT指令+ERROR 表示这条指令设置失败了
## GSM模块与单品硬件连接使用LPUART1串口 波特率 115200
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Src里新建GSM.c文件
## 在E:\BaiduSyncdisk\01BLWK_PROJETR\01cubeMX\05Camera_RF\Camera_CAT1\Core\Inc里新建GSM.h文件
## 代码中文注释 打印输出使用英文
##################################################

[08:36:34.992]收←◆^boot.rom'v\0\0\0'!\n
[08:36:36.026]收←◆
RDY

[08:36:37.994]收←◆
^MODE: 17,17

[08:36:38.180]收←◆
+E_UTRAN Service

+CGEV: ME PDN ACT 1

+NITZ: 2025/07/18,00:36:37+0,0

[08:36:43.551]发→◇ATE0
□
[08:36:43.557]收←◆ATE0

OK

[08:36:44.584]发→◇AT+CGMM
□
[08:36:44.591]收←◆
+CGMM: "Air780EG"

OK

[08:36:45.287]发→◇AT+CCID
□
[08:36:45.295]收←◆
89860316249511500582

OK

[08:36:46.200]发→◇AT+CBC
□
[08:36:46.207]收←◆
+CBC: 3489

OK

[08:36:47.488]发→◇AT+CSQ
□
[08:36:47.496]收←◆
+CSQ: 17,0

OK

[08:36:50.136]发→◇AT+CIPSTART="TCP","**********",58085
□
[08:36:50.147]收←◆
OK

[08:36:50.283]收←◆
CONNECT OK

[08:36:52.551]发→◇AT+CIPQSEND=1
□
[08:36:52.558]收←◆
OK

[08:37:00.488]发→◇AT+CIPSEND=122
□
[08:37:00.496]收←◆
>
[08:37:01.340]发→◇HY105S+12345.70898+3016.57080+052857.270625+38.9+1+6+1.7+4.20+55.6+2.9+3.0+3.8+-1.9+0.0+0.00+-128+89860316249511500582+E
□
[08:37:01.357]收←◆
DATA ACCEPT:122

[08:37:01.478]收←◆ZL+H4+F0E0+N0

[08:37:04.336]发→◇AT+CIPCLOSE
□
[08:37:04.349]收←◆
CLOSE OK



