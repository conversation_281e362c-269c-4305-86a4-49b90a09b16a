#include "lsm6ds3.h"
#include <stdio.h>  // 添加stdio.h头文件，解决printf的隐式声明警告

#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

#include <math.h>

static void WriteReg(I2C_HandleTypeDef *hi2c, uint8_t reg, uint8_t val) {
    HAL_I2C_Mem_Write(hi2c, LSM6DS3_ADDR, reg, 1, &val, 1, HAL_MAX_DELAY);
}

// 修改为全局函数，可以从外部访问
void ReadRegs(I2C_HandleTypeDef *hi2c, uint8_t reg, uint8_t *buf, uint8_t len) {
    HAL_I2C_Mem_Read(hi2c, LSM6DS3_ADDR, reg, 1, buf, len, HAL_MAX_DELAY);
}

void LSM6DS3_Init(I2C_HandleTypeDef *hi2c) {
    WriteReg(hi2c, CTRL1_XL, 0x60); // 加速度使能, 104Hz, ±2g
    WriteReg(hi2c, CTRL2_G,  0x60); // 陀螺仪使能, 104Hz, 250dps
    WriteReg(hi2c, CTRL3_C,  0x44); // 自动地址加1，启用BDU(Block Data Update)
    WriteReg(hi2c, CTRL4_C,  0x00); // 确保高性能模式
    WriteReg(hi2c, CTRL5_C,  0x00); // 确保正常模式
    WriteReg(hi2c, CTRL6_C,  0x00); // 确保正常模式
    WriteReg(hi2c, CTRL7_G,  0x00); // 确保陀螺仪正常工作模式
    WriteReg(hi2c, CTRL8_XL, 0x00); // 确保加速度计正常工作模式
    WriteReg(hi2c, CTRL9_XL, 0x38); // 启用所有轴
    WriteReg(hi2c, CTRL10_C, 0x38); // 启用所有轴

    // 读取并清除所有中断源
    uint8_t dummy;
    ReadRegs(hi2c, WAKE_UP_SRC, &dummy, 1);  // 读取并清除唤醒中断源
    ReadRegs(hi2c, TAP_SRC, &dummy, 1);      // 读取并清除敲击中断源
    ReadRegs(hi2c, D6D_SRC, &dummy, 1);      // 读取并清除方向中断源
    ReadRegs(hi2c, STATUS_REG, &dummy, 1);   // 读取状态寄存器

    printf("LSM6DS3 int OK\r\n");
		printf("GPS signal waiting......\r\n");
}


void LSM6DS3_ReadData(I2C_HandleTypeDef *hi2c, LSM6DS3_Data *data) {
    uint8_t buf[12];
    uint8_t temp_buf[2];

    // 读取陀螺仪 + 加速度数据（从 OUTX_L_G 开始）
    ReadRegs(hi2c, OUTX_L_G, buf, 12);

    int16_t gx = (int16_t)(buf[1] << 8 | buf[0]);
    int16_t gy = (int16_t)(buf[3] << 8 | buf[2]);
    int16_t gz = (int16_t)(buf[5] << 8 | buf[4]);
    int16_t ax = (int16_t)(buf[7] << 8 | buf[6]);
    int16_t ay = (int16_t)(buf[9] << 8 | buf[8]);
    int16_t az = (int16_t)(buf[11] << 8 | buf[10]);

    // 读取温度数据（必须从 OUT_TEMP_L 单独读取）
    ReadRegs(hi2c, OUT_TEMP_L, temp_buf, 2);
    int16_t temp_raw = (int16_t)(temp_buf[1] << 8 | temp_buf[0]);

    // 转换为物理单位
    data->gx = gx * 250.0f / 32768.0f; // 250 dps
    data->gy = gy * 250.0f / 32768.0f;
    data->gz = gz * 250.0f / 32768.0f;

    data->ax = ax * 2.0f / 32768.0f;   // ±2g
    data->ay = ay * 2.0f / 32768.0f;
    data->az = az * 2.0f / 32768.0f;

    data->temp_celsius = 25.0f + (float)temp_raw / 256.0f;

    // 可选：调试打印
//    printf("AX: %.2f AY: %.2f AZ: %.2f | GX: %.2f GY: %.2f GZ: %.2f | Temp: %.2f°C\r\n",
//           data->ax, data->ay, data->az,
//           data->gx, data->gy, data->gz,
//           data->temp_celsius);
}


float LSM6DS3_GetPitch(const LSM6DS3_Data *data) {
    return atan2f(data->ax, sqrtf(data->ay * data->ay + data->az * data->az)) * 180.0f / M_PI;
}

float LSM6DS3_GetRoll(const LSM6DS3_Data *data) {
    return atan2f(data->ay, sqrtf(data->ax * data->ax + data->az * data->az)) * 180.0f / M_PI;
}

/**
 * @brief 初始化姿态角结构体
 * @param attitude 姿态角结构体指针
 */
void LSM6DS3_InitAttitude(LSM6DS3_Attitude *attitude) {
    attitude->pitch = 0.0f;
    attitude->roll = 0.0f;
    attitude->yaw = 0.0f;
    attitude->lastPitch = 0.0f;
    attitude->lastRoll = 0.0f;
    attitude->lastYaw = 0.0f;
    attitude->lastUpdateTime = HAL_GetTick();
    attitude->initialized = 0;
}




/**
 * @brief 互补滤波算法实现
 * @param data 传感器数据
 * @param attitude 姿态角结构体指针
 */
void LSM6DS3_ComplementaryFilter(LSM6DS3_Data *data, LSM6DS3_Attitude *attitude) {
    uint32_t now = HAL_GetTick();
    float dt;

    // 计算加速度计测量的角度
    float accel_pitch = LSM6DS3_GetPitch(data);
    float accel_roll = LSM6DS3_GetRoll(data);

    // 如果是第一次运行，初始化角度值
    if (!attitude->initialized) {
        attitude->pitch = accel_pitch;
        attitude->roll = accel_roll;
        attitude->yaw = 0.0f; // 没有磁力计，无法初始化偏航角
        attitude->lastPitch = accel_pitch;
        attitude->lastRoll = accel_roll;
        attitude->lastYaw = 0.0f;
        attitude->initialized = 1;
        attitude->lastUpdateTime = now;
        return;
    }

    // 计算时间差（秒）
    dt = (float)(now - attitude->lastUpdateTime) / 1000.0f;
    attitude->lastUpdateTime = now;

    // 防止dt过大或为零
    if (dt <= 0.0f || dt > 1.0f) {
        dt = SAMPLE_TIME; // 使用默认采样时间
    }

    // 使用陀螺仪数据计算角度变化
    // 注意：这里需要根据实际安装方向调整轴的对应关系
    float gyro_pitch = attitude->pitch + data->gx * dt;
    float gyro_roll = attitude->roll + data->gy * dt;
    float gyro_yaw = attitude->yaw + data->gz * dt;

    // 互补滤波：融合加速度计和陀螺仪数据
    // 对俯仰角和横滚角使用互补滤波
    attitude->pitch = COMP_FILTER_ALPHA * gyro_pitch + (1.0f - COMP_FILTER_ALPHA) * accel_pitch;
    attitude->roll = COMP_FILTER_ALPHA * gyro_roll + (1.0f - COMP_FILTER_ALPHA) * accel_roll;

    // 偏航角只能依靠陀螺仪积分（不准确）
		 attitude->yaw += data->gz * dt;

    // 保存当前角度值作为下次计算的基础
    attitude->lastPitch = attitude->pitch;
    attitude->lastRoll = attitude->roll;
    attitude->lastYaw = attitude->yaw;

    // 限制偏航角在0-360度范围内
    while (attitude->yaw > 360.0f) attitude->yaw -= 360.0f;
    while (attitude->yaw < 0.0f) attitude->yaw += 360.0f;

    // 打印滤波后的姿态角和温度
//    printf("Pitch: %.2f°, Roll: %.2f°, Yaw: %.2f°, Temp: %.2f°C\r\n",
//           attitude->pitch,
//           attitude->roll,
//           attitude->yaw,
//           data->temp_celsius);

}


